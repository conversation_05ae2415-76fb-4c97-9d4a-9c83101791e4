-- Migration: Implement League-Based Gamification System
-- Date: 2025-07-08
-- Description: Replace complex level management with simplified league system

-- Drop the complex level management configuration table
DROP TABLE IF EXISTS level_management_config CASCADE;

-- Create simplified league configuration table
CREATE TABLE IF NOT EXISTS league_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    league_1_discount DECIMAL(5,2) DEFAULT 0,
    league_2_discount DECIMAL(5,2) DEFAULT 5,
    league_3_discount DECIMAL(5,2) DEFAULT 10,
    league_4_discount DECIMAL(5,2) DEFAULT 15,
    league_5_discount DECIMAL(5,2) DEFAULT 20,
    global_multiplier DECIMAL(3,2) DEFAULT 1.0,
    multiplier_description TEXT DEFAULT 'Standard rate',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default league configuration
INSERT INTO league_config (
    league_1_discount,
    league_2_discount,
    league_3_discount,
    league_4_discount,
    league_5_discount,
    global_multiplier,
    multiplier_description
) VALUES (
    0,
    5,
    10,
    15,
    20,
    1.0,
    'Standard rate'
) ON CONFLICT DO NOTHING;

-- Update user_levels table to remove points_multiplier and use progressive point requirements
-- First, let's clear existing levels and create new progressive ones
DELETE FROM user_levels;

-- Create progressive levels (50 levels total, 10 per league)
-- Progressive point requirements: each level requires 20% more points than previous
INSERT INTO user_levels (level, name, minimum_points, discount_percentage, points_multiplier) VALUES
-- League 1 (Levels 1-10) - Bronze League
(1, 'Bronze I', 0, 0, 1.0),
(2, 'Bronze II', 50, 0, 1.0),
(3, 'Bronze III', 100, 0, 1.0),
(4, 'Bronze IV', 170, 0, 1.0),
(5, 'Bronze V', 250, 0, 1.0),
(6, 'Bronze VI', 350, 0, 1.0),
(7, 'Bronze VII', 470, 0, 1.0),
(8, 'Bronze VIII', 620, 0, 1.0),
(9, 'Bronze IX', 800, 0, 1.0),
(10, 'Bronze X', 1020, 0, 1.0),

-- League 2 (Levels 11-20) - Silver League
(11, 'Silver I', 1280, 5, 1.0),
(12, 'Silver II', 1580, 5, 1.0),
(13, 'Silver III', 1930, 5, 1.0),
(14, 'Silver IV', 2330, 5, 1.0),
(15, 'Silver V', 2790, 5, 1.0),
(16, 'Silver VI', 3310, 5, 1.0),
(17, 'Silver VII', 3900, 5, 1.0),
(18, 'Silver VIII', 4560, 5, 1.0),
(19, 'Silver IX', 5300, 5, 1.0),
(20, 'Silver X', 6130, 5, 1.0),

-- League 3 (Levels 21-30) - Gold League
(21, 'Gold I', 7060, 10, 1.0),
(22, 'Gold II', 8100, 10, 1.0),
(23, 'Gold III', 9260, 10, 1.0),
(24, 'Gold IV', 10550, 10, 1.0),
(25, 'Gold V', 11980, 10, 1.0),
(26, 'Gold VI', 13570, 10, 1.0),
(27, 'Gold VII', 15330, 10, 1.0),
(28, 'Gold VIII', 17280, 10, 1.0),
(29, 'Gold IX', 19440, 10, 1.0),
(30, 'Gold X', 21830, 10, 1.0),

-- League 4 (Levels 31-40) - Platinum League
(31, 'Platinum I', 24470, 15, 1.0),
(32, 'Platinum II', 27380, 15, 1.0),
(33, 'Platinum III', 30580, 15, 1.0),
(34, 'Platinum IV', 34100, 15, 1.0),
(35, 'Platinum V', 37960, 15, 1.0),
(36, 'Platinum VI', 42190, 15, 1.0),
(37, 'Platinum VII', 46820, 15, 1.0),
(38, 'Platinum VIII', 51880, 15, 1.0),
(39, 'Platinum IX', 57420, 15, 1.0),
(40, 'Platinum X', 63480, 15, 1.0),

-- League 5 (Levels 41-50) - Diamond League
(41, 'Diamond I', 70100, 20, 1.0),
(42, 'Diamond II', 77220, 20, 1.0),
(43, 'Diamond III', 84920, 20, 1.0),
(44, 'Diamond IV', 93250, 20, 1.0),
(45, 'Diamond V', 102270, 20, 1.0),
(46, 'Diamond VI', 112040, 20, 1.0),
(47, 'Diamond VII', 122630, 20, 1.0),
(48, 'Diamond VIII', 134110, 20, 1.0),
(49, 'Diamond IX', 146560, 20, 1.0),
(50, 'Diamond X', 160070, 20, 1.0);

-- Create trigger for league_config updated_at
CREATE TRIGGER update_league_config_updated_at 
    BEFORE UPDATE ON league_config 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comment explaining the league system
COMMENT ON TABLE league_config IS 'Simplified league-based gamification configuration. Every 10 levels = 1 league. Discounts are league-based, not level-based.';
COMMENT ON COLUMN league_config.global_multiplier IS 'Global temporary multiplier that applies to all users (e.g., 2x for Black Friday)';
COMMENT ON COLUMN user_levels.points_multiplier IS 'Always 1.0 in league system - multipliers are now global only';
