import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'

// Create admin client for bypassing RLS when needed
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface UserLevel {
  id: string
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

export interface GiftThreshold {
  id: string
  threshold_points: number
  gift_product_ids: string[]
  is_active: boolean
}

// Default gamification settings
const DEFAULT_POINTS_PER_CHF = parseFloat(process.env.POINTS_PER_CHF || '1')

/**
 * Get points per CHF from admin settings
 */
export async function getPointsPerCHF(): Promise<number> {
  try {
    const { data: settings } = await supabaseAdmin
      .from('site_settings')
      .select('points_per_chf')
      .maybeSingle()

    const pointsPerCHF = settings?.points_per_chf || DEFAULT_POINTS_PER_CHF
    console.log(`🎮 Gamification: Points per CHF: ${pointsPerCHF}`)
    return pointsPerCHF
  } catch (error) {
    console.error('Error fetching points per CHF setting:', error)
    return DEFAULT_POINTS_PER_CHF
  }
}

/**
 * Calculate points earned for a purchase amount
 */
export async function calculatePointsEarned(amount: number, level: UserLevel): Promise<number> {
  const pointsPerCHF = await getPointsPerCHF()
  const basePoints = amount * pointsPerCHF
  return Math.floor(basePoints * level.points_multiplier)
}

/**
 * Get user's current level based on total points
 */
export async function getUserLevel(totalPoints: number): Promise<UserLevel | null> {
  // Use admin client to ensure we can read user levels
  const { data: levels } = await supabaseAdmin
    .from('user_levels')
    .select('*')
    .lte('minimum_points', totalPoints)
    .order('level', { ascending: false })
    .limit(1)

  return levels?.[0] || null
}

/**
 * Get all user levels
 */
export async function getAllUserLevels(): Promise<UserLevel[]> {
  const supabase = await createClient()
  
  const { data: levels } = await supabase
    .from('user_levels')
    .select('*')
    .order('level')

  return levels || []
}

/**
 * Update user's level and points after a purchase
 */
export async function updateUserGamification(
  userId: string,
  purchaseAmount: number
): Promise<{ newLevel: UserLevel | null; pointsEarned: number }> {
  console.log(`🎮 Gamification: Starting update for user ${userId}, purchase amount: ${purchaseAmount}`)

  // Use admin client to ensure we can read and update user data
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('lifetime_spend, current_level, total_points')
    .eq('id', userId)
    .single()

  if (userError || !user) {
    console.error('🎮 Gamification: Error fetching user data:', userError)
    return { newLevel: null, pointsEarned: 0 }
  }

  console.log(`🎮 Gamification: Current user data:`, {
    lifetime_spend: user.lifetime_spend,
    current_level: user.current_level,
    total_points: user.total_points
  })

  const newLifetimeSpend = (user.lifetime_spend || 0) + purchaseAmount

  // Get current level info based on current points
  const currentLevel = await getUserLevel(user.total_points || 0)
  console.log(`🎮 Gamification: Current level:`, currentLevel)

  // Calculate points earned for this purchase
  const levelForPoints = currentLevel || { points_multiplier: 1.0 } as UserLevel
  const pointsEarned = await calculatePointsEarned(purchaseAmount, levelForPoints)
  const newTotalPoints = (user.total_points || 0) + pointsEarned

  console.log(`🎮 Gamification: Points calculation:`, {
    purchaseAmount,
    levelMultiplier: levelForPoints.points_multiplier,
    pointsEarned,
    newTotalPoints
  })

  // Determine new level based on new total points
  const newLevel = await getUserLevel(newTotalPoints)
  console.log(`🎮 Gamification: New level:`, newLevel)

  // Update user using admin client
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      lifetime_spend: newLifetimeSpend,
      current_level: newLevel?.level || 1,
      total_points: newTotalPoints
    })
    .eq('id', userId)

  if (updateError) {
    console.error('🎮 Gamification: Error updating user:', updateError)
    return { newLevel: null, pointsEarned: 0 }
  }

  console.log(`🎮 Gamification: Successfully updated user ${userId} with ${pointsEarned} points`)
  return { newLevel, pointsEarned }
}

/**
 * Get applicable gift thresholds for user points
 */
export async function getApplicableGifts(userPoints: number): Promise<GiftThreshold[]> {
  const supabase = await createClient()

  const { data: thresholds } = await supabase
    .from('gift_thresholds')
    .select('*')
    .eq('is_active', true)
    .lte('threshold_points', userPoints)
    .order('threshold_points', { ascending: false })

  return thresholds || []
}

/**
 * Get next gift threshold for progress display
 */
export async function getNextGiftThreshold(userPoints: number): Promise<GiftThreshold | null> {
  const supabase = await createClient()

  const { data: thresholds } = await supabase
    .from('gift_thresholds')
    .select('*')
    .eq('is_active', true)
    .gt('threshold_points', userPoints)
    .order('threshold_points', { ascending: true })
    .limit(1)

  return thresholds?.[0] || null
}

/**
 * Calculate discount for user level
 */
export function calculateLevelDiscount(amount: number, level: UserLevel | null): number {
  if (!level || !level.discount_percentage) return 0
  return amount * (level.discount_percentage / 100)
}

/**
 * Get user's discount percentage
 */
export async function getUserDiscountPercentage(userId: string): Promise<number> {
  const supabase = await createClient()

  const { data: user } = await supabase
    .from('users')
    .select('total_points')
    .eq('id', userId)
    .single()

  if (!user) return 0

  const level = await getUserLevel(user.total_points || 0)
  return level?.discount_percentage || 0
}

/**
 * Initialize default user levels if none exist
 */
export async function initializeDefaultLevels(): Promise<void> {
  const supabase = await createClient()
  
  // Check if levels already exist
  const { count } = await supabase
    .from('user_levels')
    .select('*', { count: 'exact', head: true })

  if (count && count > 0) return

  // Create default levels
  const defaultLevels = [
    {
      level: 1,
      name: 'Bronze',
      minimum_points: 0,
      discount_percentage: 0,
      points_multiplier: 1.0
    },
    {
      level: 2,
      name: 'Silver',
      minimum_points: 200,
      discount_percentage: 5,
      points_multiplier: 1.2
    },
    {
      level: 3,
      name: 'Gold',
      minimum_points: 500,
      discount_percentage: 10,
      points_multiplier: 1.5
    },
    {
      level: 4,
      name: 'Platinum',
      minimum_points: 1000,
      discount_percentage: 15,
      points_multiplier: 2.0
    }
  ]

  await supabase
    .from('user_levels')
    .insert(defaultLevels)
}

/**
 * Initialize default gift thresholds if none exist
 */
export async function initializeDefaultGiftThresholds(): Promise<void> {
  const supabase = await createClient()
  
  // Check if thresholds already exist
  const { count } = await supabase
    .from('gift_thresholds')
    .select('*', { count: 'exact', head: true })

  if (count && count > 0) return

  // Get some product IDs for gifts (you'll need to adjust these)
  const { data: products } = await supabase
    .from('products')
    .select('id')
    .eq('category', 'accessories')
    .limit(3)

  if (!products || products.length === 0) return

  // Create default gift thresholds (based on points)
  const defaultThresholds = [
    {
      threshold_points: 75,
      gift_product_ids: [products[0]?.id].filter(Boolean),
      is_active: true
    },
    {
      threshold_points: 150,
      gift_product_ids: products.slice(0, 2).map(p => p.id),
      is_active: true
    },
    {
      threshold_points: 250,
      gift_product_ids: products.map(p => p.id),
      is_active: true
    }
  ]

  await supabase
    .from('gift_thresholds')
    .insert(defaultThresholds)
}
