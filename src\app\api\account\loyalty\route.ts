import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getUserLevel } from '@/lib/gamification';

export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('total_points, lifetime_spend')
      .eq('id', user.id)
      .single();
    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get user level information based on total points
    const currentLevel = await getUserLevel(profile.total_points || 0);

    // Get next level information
    const { data: nextLevel } = await supabase
      .from('user_levels')
      .select('*')
      .gt('minimum_points', profile.total_points || 0)
      .order('minimum_points', { ascending: true })
      .limit(1)
      .single();

    // Calculate points needed for next level
    const pointsToNext = nextLevel ? nextLevel.minimum_points - (profile.total_points || 0) : 0;

    // Get next gift threshold based on points (simplified)
    const { data: nextGift } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .gt('threshold_points', profile.total_points || 0)
      .order('threshold_points', { ascending: true })
      .limit(1)
      .single();

    // Calculate points needed for next gift
    const pointsToNextGift = nextGift
      ? nextGift.threshold_points - (profile.total_points || 0)
      : 0;

    return NextResponse.json({
      currentLevel: currentLevel?.level || 1,
      currentLevelName: currentLevel?.name || 'Bronze',
      totalPoints: profile.total_points || 0,
      lifetimeSpend: profile.lifetime_spend || 0,
      pointsToNext,
      nextLevelName: nextLevel?.name || null,
      discountPercent: currentLevel?.discount_percentage || 0,
      pointsMultiplier: currentLevel?.points_multiplier || 1,
      pointsToNextGift,
      nextGiftThreshold: nextGift?.threshold_points || null
    });
  } catch (error) {
    console.error('Error in loyalty API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
