'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'

interface GiftThreshold {
  id?: string
  threshold_points: number
  gift_product_ids: string[]
  is_active: boolean
}

interface GiftThresholdFormProps {
  locale: string
  gift?: GiftThreshold
}

export default function GiftThresholdForm({ locale, gift }: GiftThresholdFormProps) {
  const supabase = createClient()
  const [loading, setLoading] = useState(false)
  const [products, setProducts] = useState<{ id: string; title: string }[]>([])

  useEffect(() => {
    const loadProducts = async () => {
      const { data, error } = await supabase
        .from('products')
        .select('id, title')
        .eq('is_available', true)
        .order('title')
      if (!error && data) setProducts(data as { id: string; title: string }[])
    }
    loadProducts()
  }, [supabase])
  const [formData, setFormData] = useState<GiftThreshold>({
    id: gift?.id,
    threshold_points: gift?.threshold_points || 0,
    gift_product_ids: gift?.gift_product_ids || [],
    is_active: gift?.is_active ?? true,
  })

  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    const method = gift ? 'PATCH' : 'POST'
    const url = gift ? `/api/admin/gamification/gifts/${gift.id}` : '/api/admin/gamification/gifts'
    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      if (response.ok) {
        toast({ title: t('gifts.form.savedTitle'), description: t('gifts.form.savedMessage') })
        router.push(`/${locale}/admin/gamification`)
      } else {
        const err = await response.json()
        throw new Error(err.error || 'Error')
      }
    } catch (err) {
      console.error('Error saving gift threshold:', err)
      toast({
        title: t('gifts.form.saveErrorTitle'),
        description: t('gifts.form.saveErrorMessage'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="threshold_points">{t('gifts.form.pointsRequired')} *</Label>
            <Input
              id="threshold_points"
              type="number"
              value={formData.threshold_points}
              onChange={(e) => setFormData({ ...formData, threshold_points: parseInt(e.target.value) })}
              required
            />
          </div>
          <div>
            <Label htmlFor="gift_product_id">{t('gifts.form.giftProduct')} *</Label>
            <Select
              value={formData.gift_product_ids[0] ?? ''}
              onValueChange={(value) =>
                setFormData({ ...formData, gift_product_ids: value ? [value] : [] })
              }
            >
              <SelectTrigger id="gift_product_id">
                <SelectValue placeholder={t('gifts.form.selectProduct')} />
              </SelectTrigger>
              <SelectContent>
                {products.map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mt-6">
            <Checkbox
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: Boolean(checked) })}
            />
            <Label htmlFor="is_active">{t('gifts.form.active')}</Label>
          </div>
        </div>
      </div>
      <Button type="submit" disabled={loading}>
        {loading ? t('gifts.form.saving') : t('gifts.form.save')}
      </Button>
    </form>
  )
}

